
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 200 100% 60%;
    --primary-foreground: 240 10% 3.9%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 160 60% 45%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 200 100% 60%;

    --radius: 0.75rem;

    --sidebar-background: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 200 100% 60%;
    --sidebar-primary-foreground: 240 10% 3.9%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 200 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-cairo;
    direction: rtl;
  }

  /* Arabic text styling */
  .arabic-text {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    direction: rtl;
    text-align: right;
  }

  /* Custom gradients */
  .gradient-taxi {
    background: linear-gradient(135deg, #0ea5e9 0%, #10b981 50%, #8b5cf6 100%);
  }

  .gradient-taxi-light {
    background: linear-gradient(135deg, #e0f2fe 0%, #ecfdf5 50%, #f5f3ff 100%);
  }

  /* Map container styling */
  .map-container {
    width: 100%;
    height: 100%;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #0ea5e9, #10b981);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0284c7, #059669);
  }

  /* Leaflet map RTL support */
  .leaflet-control-container .leaflet-top.leaflet-left {
    top: 10px;
    right: 10px;
    left: auto;
  }

  .leaflet-control-container .leaflet-top.leaflet-right {
    top: 10px;
    left: 10px;
    right: auto;
  }

  .leaflet-control-container .leaflet-bottom.leaflet-left {
    bottom: 10px;
    right: 10px;
    left: auto;
  }

  .leaflet-control-container .leaflet-bottom.leaflet-right {
    bottom: 10px;
    left: 10px;
    right: auto;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* Custom input styling for Arabic */
input[type="text"], input[type="tel"], textarea, select {
  direction: rtl;
  text-align: right;
}

input[type="text"]::placeholder, 
input[type="tel"]::placeholder, 
textarea::placeholder {
  text-align: right;
  direction: rtl;
}

/* Custom button hover effects */
.btn-taxi {
  @apply bg-gradient-to-r from-taxi-500 to-emerald-500 hover:from-taxi-600 hover:to-emerald-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

/* Loading spinner */
.loading-spinner {
  border: 3px solid transparent;
  border-top: 3px solid #0ea5e9;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification styles */
.notification-enter {
  opacity: 0;
  transform: translateX(100%);
}

.notification-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.notification-exit {
  opacity: 1;
}

.notification-exit-active {
  opacity: 0;
  transform: translateX(100%);
  transition: opacity 300ms, transform 300ms;
}
